"""
Crée un fichier de test avec la structure appropriée pour tester la consolidation
"""

import pandas as pd
from datetime import datetime

def create_test_file():
    """Crée un fichier de test avec la feuille 'Informations Commune'"""
    
    # Données de test pour la feuille "Informations Commune"
    test_data = {
        'Nom de commune': ['Test Commune'],
        'ID tâche Plan Adressage': ['PA-TEST-001'],
        'Code INSEE': ['99999'],
        'Nbr des voies CM': [5],
        'Nbr des IMB PA': [25],
        'Date d\'affectation': [datetime.now().strftime("%d/%m/%Y")],
        'Date Livraison': [''],
        'Etat Ticket PA': ['En cours'],
        'Durée Totale CM': [15],
        'Duréé Totale PA': [30],
        'Durée Finale': [45],
        'ID Tache 501/511': [''],
        'Date Dépose Ticket 501/511': [''],
        'Dépose Ticket UPR': ['Non Créé'],
        'ID tâche UPR': [''],
        'Collaborateur': ['ELJ Wissem']
    }
    
    df_commune = pd.DataFrame(test_data)
    
    # Créer aussi des feuilles factices pour CM Adresse et Plan Adressage
    df_cm = pd.DataFrame({
        'Domaine': ['Orange'],
        'ID Tache': ['CM-001'],
        'Commune': ['Test Commune'],
        'Code INSEE': ['99999'],
        'Voie demandé': ['Rue de Test']
    })
    
    df_plan = pd.DataFrame({
        'A': ['IMB-001'],
        'B': ['Test'],
        'C': ['Adresse'],
        'D': ['Test']
    })
    
    # Sauvegarder le fichier
    filename = "Test_Suivi_Commune_PA-TEST-001_99999.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df_cm.to_excel(writer, sheet_name="PA-TEST-001-CM Adresse", index=False)
        df_plan.to_excel(writer, sheet_name="PA-TEST-001-Plan Adressage", index=False)
        df_commune.to_excel(writer, sheet_name="PA-TEST-001-Informations Commune", index=False)
    
    print(f"✅ Fichier de test créé: {filename}")
    print(f"   Feuilles: CM Adresse, Plan Adressage, Informations Commune")
    print(f"   Données: {test_data}")
    
    return filename

if __name__ == "__main__":
    create_test_file()
