"""
Script pour trouver et configurer le fichier Suivi Global original (20250603_1305)
qui correspond au lien SharePoint fourni
"""

import os
import shutil
from pathlib import Path

def find_original_suivi_global():
    """Trouve le fichier Suivi Global original (20250603_1305)"""
    
    target_filename = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    user_profile = os.path.expanduser("~")
    
    print(f"🔍 Recherche du fichier original: {target_filename}")
    
    # Chemins de recherche
    search_paths = [
        # OneDrive principal
        os.path.join(user_profile, "OneDrive - orange.com"),
        
        # Dossier Teams synchronisé
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        
        # Dossier de travail actuel
        ".",
    ]
    
    found_files = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"📁 Recherche dans: {search_path}")
            
            try:
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file == target_filename:
                            full_path = os.path.join(root, file)
                            found_files.append(full_path)
                            print(f"   ✅ Trouvé: {full_path}")
                            
                            # Vérifier les détails du fichier
                            try:
                                stat = os.stat(full_path)
                                size_kb = stat.st_size / 1024
                                from datetime import datetime
                                mod_time = datetime.fromtimestamp(stat.st_mtime)
                                print(f"      📏 Taille: {size_kb:.1f} KB")
                                print(f"      📅 Modifié: {mod_time.strftime('%d/%m/%Y %H:%M')}")
                            except Exception as e:
                                print(f"      ⚠️ Erreur info: {e}")
                                
            except Exception as e:
                print(f"   ❌ Erreur recherche: {e}")
    
    return found_files

def ensure_file_in_teams_sync():
    """S'assure que le fichier original est dans le dossier Teams synchronisé"""
    
    target_filename = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    user_profile = os.path.expanduser("~")
    
    # Dossier Teams cible
    teams_folder = os.path.join(
        user_profile, 
        "OneDrive - orange.com", 
        "BOTG2R-CMAdressesetPlanAdressage - Documents partagés",
        "CM Adresses et Plan Adressage",
        "Suivie Plan Adressage"
    )
    
    teams_file_path = os.path.join(teams_folder, target_filename)
    
    print(f"\n🎯 Vérification du fichier dans Teams:")
    print(f"   Chemin cible: {teams_file_path}")
    
    if os.path.exists(teams_file_path):
        print(f"   ✅ Le fichier existe déjà dans Teams")
        
        # Vérifier qu'il est accessible
        try:
            import pandas as pd
            df = pd.read_excel(teams_file_path)
            print(f"   📖 Test lecture: {len(df)} lignes, {len(df.columns)} colonnes")
            print(f"   ✅ Le fichier Teams est prêt pour la consolidation!")
            return teams_file_path
        except Exception as e:
            print(f"   ❌ Erreur lecture: {e}")
            return None
    else:
        print(f"   ❌ Le fichier n'existe pas dans Teams")
        
        # Chercher le fichier ailleurs et le copier
        found_files = find_original_suivi_global()
        
        if found_files:
            # Prendre le premier fichier trouvé (hors Teams)
            source_file = None
            for file_path in found_files:
                if "BOTG2R-CMAdressesetPlanAdressage" not in file_path:
                    source_file = file_path
                    break
            
            if not source_file:
                source_file = found_files[0]  # Prendre le premier disponible
            
            print(f"\n📋 Copie du fichier vers Teams:")
            print(f"   Source: {source_file}")
            print(f"   Destination: {teams_file_path}")
            
            try:
                # S'assurer que le dossier existe
                os.makedirs(teams_folder, exist_ok=True)
                
                # Copier le fichier
                shutil.copy2(source_file, teams_file_path)
                print(f"   ✅ Fichier copié avec succès!")
                
                # Vérifier la copie
                if os.path.exists(teams_file_path):
                    import pandas as pd
                    df = pd.read_excel(teams_file_path)
                    print(f"   📖 Vérification: {len(df)} lignes, {len(df.columns)} colonnes")
                    return teams_file_path
                
            except Exception as e:
                print(f"   ❌ Erreur copie: {e}")
                return None
        else:
            print(f"   ❌ Fichier original non trouvé nulle part")
            return None

def configure_for_sharepoint_sync():
    """Configure le système pour synchroniser avec SharePoint"""
    
    teams_file = ensure_file_in_teams_sync()
    
    if teams_file:
        print(f"\n🔧 Configuration SharePoint:")
        print(f"   ✅ Fichier configuré: {os.path.basename(teams_file)}")
        print(f"   📍 Emplacement: {teams_file}")
        print(f"   🔗 URL SharePoint: https://orange0.sharepoint.com/.../Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
        print(f"   🔄 Synchronisation: Automatique via OneDrive")
        
        print(f"\n📋 RÉSULTAT:")
        print(f"   ✅ Le consolidateur mettra à jour le fichier SharePoint original")
        print(f"   ✅ Les modifications apparaîtront automatiquement en ligne")
        print(f"   ✅ L'URL SharePoint reflétera les changements")
        
        return True
    else:
        print(f"\n❌ Configuration échouée")
        print(f"   Le fichier original n'a pas pu être configuré pour Teams")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 CONFIGURATION FICHIER SHAREPOINT ORIGINAL")
    print("=" * 70)
    print("Fichier cible: Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    print("URL: https://orange0.sharepoint.com/.../Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    print("=" * 70)
    
    success = configure_for_sharepoint_sync()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 CONFIGURATION RÉUSSIE!")
        print("Le consolidateur mettra à jour le fichier SharePoint original")
    else:
        print("❌ CONFIGURATION ÉCHOUÉE")
        print("Vérifiez que le fichier original existe")
    print("=" * 70)
