"""
Script pour localiser et ouvrir le dossier Teams sync
"""

import os
import subprocess
import webbrowser
from pathlib import Path

def find_teams_sync_folder():
    """Trouve et affiche tous les dossiers Teams possibles"""
    
    user_profile = os.path.expanduser("~")
    print(f"👤 Profil utilisateur: {user_profile}")
    
    # Dossiers Teams possibles
    teams_paths = [
        # OneDrive principal
        os.path.join(user_profile, "OneDrive - orange.com"),
        
        # Do<PERSON>r Teams spécifique
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        
        # Autres dossiers OneDrive
        os.path.join(user_profile, "OneDrive"),
        os.path.join(user_profile, "OneDrive - orange.com", "Documents partagés"),
    ]
    
    print("\n📁 DOSSIERS TEAMS DÉTECTÉS:")
    print("=" * 50)
    
    existing_folders = []
    
    for i, path in enumerate(teams_paths, 1):
        exists = os.path.exists(path)
        status = "✅ EXISTE" if exists else "❌ N'existe pas"
        
        print(f"{i}. {status}")
        print(f"   📍 {path}")
        
        if exists:
            existing_folders.append(path)
            
            # Afficher le contenu
            try:
                contents = os.listdir(path)
                if contents:
                    print(f"   📄 Contenu ({len(contents)} éléments):")
                    for item in contents[:3]:  # Afficher les 3 premiers
                        print(f"      • {item}")
                    if len(contents) > 3:
                        print(f"      • ... et {len(contents) - 3} autres")
                else:
                    print(f"   📄 Dossier vide")
            except PermissionError:
                print(f"   🔒 Accès refusé")
            except Exception as e:
                print(f"   ⚠️ Erreur: {e}")
        
        print()
    
    return existing_folders

def open_teams_folder():
    """Ouvre le dossier Teams dans l'explorateur Windows"""
    
    existing_folders = find_teams_sync_folder()
    
    if not existing_folders:
        print("❌ Aucun dossier Teams trouvé!")
        print("\n💡 SOLUTIONS:")
        print("1. Vérifiez que OneDrive est installé et connecté")
        print("2. Vérifiez que Teams est synchronisé")
        print("3. Ouvrez OneDrive manuellement")
        return False
    
    # Choisir le meilleur dossier à ouvrir
    target_folder = None
    
    # Priorité 1: Dossier Suivie Plan Adressage s'il existe
    for folder in existing_folders:
        if "Suivie Plan Adressage" in folder:
            target_folder = folder
            break
    
    # Priorité 2: Dossier CM Adresses s'il existe
    if not target_folder:
        for folder in existing_folders:
            if "CM Adresses" in folder:
                target_folder = folder
                break
    
    # Priorité 3: Dossier BOTG2R s'il existe
    if not target_folder:
        for folder in existing_folders:
            if "BOTG2R" in folder:
                target_folder = folder
                break
    
    # Priorité 4: OneDrive principal
    if not target_folder:
        target_folder = existing_folders[0]
    
    print(f"🎯 OUVERTURE DU DOSSIER:")
    print(f"📍 {target_folder}")
    
    try:
        # Ouvrir dans l'explorateur Windows
        subprocess.run(['explorer', target_folder], check=True)
        print(f"✅ Dossier ouvert dans l'explorateur Windows")
        
        return target_folder
        
    except Exception as e:
        print(f"❌ Erreur ouverture: {e}")
        print(f"📋 Copiez ce chemin manuellement: {target_folder}")
        return target_folder

def show_manual_instructions():
    """Affiche les instructions manuelles pour trouver Teams"""
    
    print("\n" + "=" * 60)
    print("📋 INSTRUCTIONS MANUELLES - TROUVER TEAMS SYNC")
    print("=" * 60)
    
    print("\n🔍 MÉTHODE 1 - Via l'explorateur Windows:")
    print("1. Ouvrez l'explorateur Windows (Win + E)")
    print("2. Dans la barre d'adresse, tapez: %USERPROFILE%")
    print("3. Cherchez le dossier: 'OneDrive - orange.com'")
    print("4. Ouvrez-le")
    print("5. Cherchez: 'BOTG2R-CMAdressesetPlanAdressage - Documents partagés'")
    
    print("\n🔍 MÉTHODE 2 - Via OneDrive:")
    print("1. Clic droit sur l'icône OneDrive dans la barre des tâches")
    print("2. Cliquez 'Ouvrir le dossier'")
    print("3. Cherchez le dossier Teams")
    
    print("\n🔍 MÉTHODE 3 - Via Teams:")
    print("1. Ouvrez Microsoft Teams")
    print("2. Allez dans le canal: BOTG2R-CMAdressesetPlanAdressage")
    print("3. Cliquez sur 'Fichiers'")
    print("4. Cliquez sur 'Ouvrir dans SharePoint'")
    print("5. Dans SharePoint, cliquez 'Synchroniser'")
    
    print("\n🎯 DOSSIER CIBLE:")
    user_profile = os.path.expanduser("~")
    target = os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage")
    print(f"📍 {target}")

def check_onedrive_status():
    """Vérifie le statut d'OneDrive"""
    
    print("\n🔍 VÉRIFICATION ONEDRIVE:")
    print("=" * 30)
    
    user_profile = os.path.expanduser("~")
    onedrive_paths = [
        os.path.join(user_profile, "OneDrive - orange.com"),
        os.path.join(user_profile, "OneDrive"),
    ]
    
    for path in onedrive_paths:
        if os.path.exists(path):
            print(f"✅ OneDrive trouvé: {path}")
            
            # Vérifier s'il y a des dossiers Teams
            try:
                contents = os.listdir(path)
                teams_folders = [f for f in contents if "botg2r" in f.lower() or "documents partagés" in f.lower()]
                
                if teams_folders:
                    print(f"📁 Dossiers Teams détectés:")
                    for folder in teams_folders:
                        print(f"   • {folder}")
                else:
                    print(f"⚠️ Aucun dossier Teams détecté")
                    print(f"💡 Vous devez synchroniser le canal Teams")
                    
            except Exception as e:
                print(f"❌ Erreur lecture: {e}")
        else:
            print(f"❌ OneDrive non trouvé: {path}")

if __name__ == "__main__":
    print("=" * 70)
    print("📁 LOCALISATION DOSSIER TEAMS SYNC")
    print("=" * 70)
    
    # Vérifier OneDrive
    check_onedrive_status()
    
    # Trouver et ouvrir le dossier Teams
    opened_folder = open_teams_folder()
    
    if opened_folder:
        print(f"\n🎉 SUCCÈS!")
        print(f"Le dossier Teams a été ouvert dans l'explorateur")
        print(f"📍 Emplacement: {opened_folder}")
        
        print(f"\n📋 PROCHAINES ÉTAPES:")
        print(f"1. Dans l'explorateur qui s'est ouvert, naviguez vers le dossier final")
        print(f"2. Placez-y votre fichier Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
        print(f"3. Utilisez le consolidateur avec 'Auto-Find'")
    else:
        show_manual_instructions()
    
    print("\n" + "=" * 70)
