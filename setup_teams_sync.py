"""
Script pour configurer la synchronisation Teams et déplacer le fichier Suivi Global
vers l'emplacement Teams synchronisé pour mise à jour en ligne
"""

import os
import shutil
from pathlib import Path

def find_teams_sync_folder():
    """Trouve le dossier Teams synchronisé pour BOTG2R-CMAdressesetPlanAdressage"""
    user_profile = os.path.expanduser("~")
    
    # Chemins possibles pour la synchronisation Teams
    possible_paths = [
        # Format standard Teams
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        
        # Format alternatif
        os.path.join(user_profile, "OneDrive - orange.com", "Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        os.path.join(user_profile, "OneDrive - orange.com", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        
        # Recherche dans tous les sous-dossiers OneDrive
    ]
    
    print("🔍 Recherche du dossier Teams synchronisé...")
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Dossier Teams trouvé: {path}")
            return path
    
    # Recherche plus large dans OneDrive
    onedrive_root = os.path.join(user_profile, "OneDrive - orange.com")
    if os.path.exists(onedrive_root):
        print(f"🔍 Recherche dans OneDrive: {onedrive_root}")
        
        for root, dirs, files in os.walk(onedrive_root):
            # Chercher des dossiers contenant "BOTG2R" ou "Plan Adressage"
            for dir_name in dirs:
                if any(keyword in dir_name.lower() for keyword in ["botg2r", "plan adressage", "suivie"]):
                    full_path = os.path.join(root, dir_name)
                    print(f"📁 Dossier candidat trouvé: {full_path}")
                    
                    # Vérifier si c'est le bon dossier
                    if "suivie" in dir_name.lower() and "plan" in dir_name.lower():
                        print(f"✅ Dossier Teams probable: {full_path}")
                        return full_path
    
    return None

def create_teams_sync_structure():
    """Crée la structure de dossiers Teams si elle n'existe pas"""
    user_profile = os.path.expanduser("~")
    
    # Structure cible
    target_path = os.path.join(
        user_profile, 
        "OneDrive - orange.com", 
        "BOTG2R-CMAdressesetPlanAdressage - Documents partagés",
        "CM Adresses et Plan Adressage",
        "Suivie Plan Adressage"
    )
    
    print(f"📁 Création de la structure Teams: {target_path}")
    
    try:
        os.makedirs(target_path, exist_ok=True)
        print(f"✅ Structure créée avec succès")
        return target_path
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        return None

def move_suivi_global_to_teams():
    """Déplace le fichier Suivi Global vers le dossier Teams synchronisé"""
    
    # Fichier source (actuel)
    current_file = "Suivi_Global_Plan_Adressage_20250603_1340.xlsx"
    
    if not os.path.exists(current_file):
        print(f"❌ Fichier source non trouvé: {current_file}")
        return False
    
    # Trouver ou créer le dossier Teams
    teams_folder = find_teams_sync_folder()
    
    if not teams_folder:
        print("📁 Dossier Teams non trouvé, création de la structure...")
        teams_folder = create_teams_sync_structure()
    
    if not teams_folder:
        print("❌ Impossible de créer/trouver le dossier Teams")
        return False
    
    # Chemin de destination
    destination = os.path.join(teams_folder, current_file)
    
    print(f"📋 Déplacement du fichier:")
    print(f"   Source: {os.path.abspath(current_file)}")
    print(f"   Destination: {destination}")
    
    try:
        # Créer une sauvegarde si le fichier existe déjà
        if os.path.exists(destination):
            backup_name = f"{current_file}.backup"
            backup_path = os.path.join(teams_folder, backup_name)
            shutil.copy2(destination, backup_path)
            print(f"💾 Sauvegarde créée: {backup_name}")
        
        # Copier le fichier (garder l'original pour sécurité)
        shutil.copy2(current_file, destination)
        print(f"✅ Fichier copié vers Teams avec succès!")
        
        # Vérifier que le fichier est accessible
        if os.path.exists(destination):
            size = os.path.getsize(destination) / 1024
            print(f"📊 Fichier Teams: {size:.1f} KB")
            
            # Test de lecture
            try:
                import pandas as pd
                df = pd.read_excel(destination)
                print(f"📖 Test lecture: {len(df)} lignes, {len(df.columns)} colonnes")
                print(f"✅ Le fichier Teams est prêt pour la consolidation!")
                
                return destination
                
            except Exception as e:
                print(f"❌ Erreur test lecture: {e}")
                return False
        
    except Exception as e:
        print(f"❌ Erreur lors du déplacement: {e}")
        return False

def update_consolidator_config():
    """Met à jour la configuration du consolidateur pour pointer vers le fichier Teams"""
    
    teams_file = move_suivi_global_to_teams()
    
    if teams_file:
        print(f"\n🔧 Configuration du consolidateur:")
        print(f"   Le fichier Suivi Global est maintenant dans Teams")
        print(f"   Chemin: {teams_file}")
        print(f"   ✅ Les modifications seront synchronisées automatiquement avec SharePoint")
        
        # Instructions pour l'utilisateur
        print(f"\n📋 INSTRUCTIONS:")
        print(f"   1. Le fichier Suivi Global est maintenant dans le dossier Teams synchronisé")
        print(f"   2. Utilisez le bouton 'Auto-Find' dans le consolidateur")
        print(f"   3. Toutes les modifications seront automatiquement synchronisées en ligne")
        print(f"   4. Le fichier SharePoint sera mis à jour automatiquement")
        
        return True
    
    return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 CONFIGURATION TEAMS SYNC - SUIVI GLOBAL")
    print("=" * 70)
    
    success = update_consolidator_config()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 CONFIGURATION RÉUSSIE!")
        print("Le fichier Suivi Global est maintenant synchronisé avec Teams/SharePoint")
    else:
        print("❌ CONFIGURATION ÉCHOUÉE")
        print("Utilisez la sélection manuelle dans le consolidateur")
    print("=" * 70)
