"""
Script pour déplacer le fichier téléchargé vers le dossier Teams
"""

import os
import shutil
from pathlib import Path

def find_downloaded_file():
    """Trouve le fichier téléchargé dans les dossiers communs"""
    
    target_filename = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    user_profile = os.path.expanduser("~")
    
    # Dossiers de recherche communs
    search_folders = [
        os.path.join(user_profile, "Downloads"),
        os.path.join(user_profile, "Téléchargements"),
        os.path.join(user_profile, "Desktop"),
        os.path.join(user_profile, "Bureau"),
        ".",  # Dossier actuel
    ]
    
    print(f"🔍 Recherche du fichier téléchargé: {target_filename}")
    
    found_files = []
    
    for folder in search_folders:
        if os.path.exists(folder):
            print(f"📁 Recherche dans: {folder}")
            
            try:
                files = os.listdir(folder)
                for file in files:
                    if file == target_filename or file.startswith("Suivi_Global_Plan_Adressage_20250603_1305"):
                        full_path = os.path.join(folder, file)
                        found_files.append(full_path)
                        print(f"   ✅ Trouvé: {file}")
                        
                        # Vérifier la taille
                        try:
                            size_kb = os.path.getsize(full_path) / 1024
                            print(f"      📏 Taille: {size_kb:.1f} KB")
                        except:
                            pass
                            
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
    
    return found_files

def move_to_teams_folder():
    """Déplace le fichier téléchargé vers le dossier Teams"""
    
    # Trouver le fichier téléchargé
    found_files = find_downloaded_file()
    
    if not found_files:
        print(f"\n❌ Aucun fichier trouvé")
        print(f"📋 Instructions:")
        print(f"   1. Téléchargez le fichier depuis SharePoint")
        print(f"   2. Sauvegardez-le dans Downloads ou sur le Bureau")
        print(f"   3. Relancez ce script")
        return False
    
    # Prendre le premier fichier trouvé
    source_file = found_files[0]
    print(f"\n📄 Fichier source: {source_file}")
    
    # Dossier Teams de destination
    user_profile = os.path.expanduser("~")
    teams_folder = os.path.join(
        user_profile, 
        "OneDrive - orange.com", 
        "BOTG2R-CMAdressesetPlanAdressage - Documents partagés",
        "CM Adresses et Plan Adressage",
        "Suivie Plan Adressage"
    )
    
    destination_file = os.path.join(teams_folder, "Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    
    print(f"📁 Dossier Teams: {teams_folder}")
    print(f"📄 Fichier destination: {destination_file}")
    
    try:
        # S'assurer que le dossier existe
        os.makedirs(teams_folder, exist_ok=True)
        print(f"✅ Dossier Teams prêt")
        
        # Copier le fichier
        shutil.copy2(source_file, destination_file)
        print(f"✅ Fichier copié avec succès!")
        
        # Vérifier la copie
        if os.path.exists(destination_file):
            import pandas as pd
            df = pd.read_excel(destination_file)
            size_kb = os.path.getsize(destination_file) / 1024
            
            print(f"\n🎉 SUCCÈS!")
            print(f"📊 Fichier: {size_kb:.1f} KB, {len(df)} lignes, {len(df.columns)} colonnes")
            print(f"📍 Emplacement: {destination_file}")
            print(f"")
            print(f"✅ Le fichier SharePoint est maintenant synchronisé localement")
            print(f"✅ Le consolidateur peut maintenant le mettre à jour")
            print(f"✅ Les modifications seront synchronisées automatiquement vers SharePoint")
            
            return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la copie: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("📁 DÉPLACEMENT FICHIER TÉLÉCHARGÉ VERS TEAMS")
    print("=" * 70)
    
    success = move_to_teams_folder()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 FICHIER CONFIGURÉ AVEC SUCCÈS!")
        print("Vous pouvez maintenant utiliser le consolidateur")
    else:
        print("❌ FICHIER NON TROUVÉ")
        print("Téléchargez d'abord le fichier depuis SharePoint")
    print("=" * 70)
