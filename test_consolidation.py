"""
Test script pour déboguer la fonctionnalité de consolidation
"""

import pandas as pd
import os
import sys
from datetime import datetime

def test_read_excel_files():
    """Test de lecture des fichiers Excel"""
    print("🔍 Test de lecture des fichiers Excel...")
    
    # Tester le fichier Suivi Global
    global_file = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    if os.path.exists(global_file):
        try:
            df_global = pd.read_excel(global_file)
            print(f"✅ Suivi Global lu avec succès: {len(df_global)} lignes, {len(df_global.columns)} colonnes")
            print(f"   Colonnes: {list(df_global.columns)}")
        except Exception as e:
            print(f"❌ Erreur lecture Suivi Global: {e}")
    else:
        print(f"❌ Fichier Suivi Global non trouvé: {global_file}")
    
    # Chercher des fichiers de test
    test_files = []
    for file in os.listdir("."):
        if (file.startswith("Suivi_") or file.startswith("Test_Suivi")) and file.endswith(".xlsx") and file != global_file:
            test_files.append(file)
    
    print(f"\n📄 Fichiers de test trouvés: {len(test_files)}")
    
    for test_file in test_files[:2]:  # Tester max 2 fichiers
        try:
            excel_file = pd.ExcelFile(test_file)
            print(f"✅ {test_file}:")
            print(f"   Feuilles: {excel_file.sheet_names}")
            
            # Chercher la feuille "Informations Commune"
            target_sheet = None
            for sheet_name in excel_file.sheet_names:
                if "informations commune" in sheet_name.lower():
                    target_sheet = sheet_name
                    break
            
            if target_sheet:
                df = pd.read_excel(test_file, sheet_name=target_sheet)
                print(f"   Feuille '{target_sheet}': {len(df)} lignes, {len(df.columns)} colonnes")
                print(f"   Colonnes: {list(df.columns)}")
                
                # Afficher les premières données
                if not df.empty:
                    print(f"   Première ligne:")
                    for col in df.columns:
                        value = df.iloc[0][col] if len(df) > 0 else "N/A"
                        print(f"     {col}: {value}")
            else:
                print(f"   ❌ Aucune feuille 'Informations Commune' trouvée")
                
        except Exception as e:
            print(f"❌ Erreur lecture {test_file}: {e}")

def test_consolidation_logic():
    """Test de la logique de consolidation"""
    print("\n🔄 Test de la logique de consolidation...")
    
    # Créer des données de test
    df_global = pd.DataFrame({
        'Nom de commune': ['Commune A', 'Commune B'],
        'Code INSEE': ['12345', '67890'],
        'ID tâche Plan Adressage': ['PA-001', 'PA-002'],
        'Etat Ticket PA': ['En cours', 'Livré'],
        'Collaborateur': ['Test User', 'Test User 2']
    })
    
    df_new = pd.DataFrame({
        'Nom de commune': ['Commune A', 'Commune C'],  # A existe, C est nouveau
        'Code INSEE': ['12345', '11111'],
        'ID tâche Plan Adressage': ['PA-001-Updated', 'PA-003'],
        'Etat Ticket PA': ['Livré', 'En cours'],  # Mise à jour pour A
        'Collaborateur': ['Updated User', 'New User'],
        'Source_Fichier': ['test.xlsx', 'test.xlsx'],
        'Date_Consolidation': ['03/06/2024 13:30', '03/06/2024 13:30']
    })
    
    print("📊 Données globales initiales:")
    print(df_global)
    print("\n📊 Nouvelles données à consolider:")
    print(df_new)
    
    # Test de fusion
    try:
        # Logique de fusion simplifiée
        key_column = 'Code INSEE'
        df_result = df_global.copy()
        
        for _, new_row in df_new.iterrows():
            key_value = new_row[key_column]
            existing_mask = df_result[key_column] == key_value
            
            if existing_mask.any():
                # Mettre à jour
                existing_index = df_result[existing_mask].index[0]
                print(f"\n🔄 Mise à jour de la ligne existante pour {key_column}: {key_value}")
                for col in new_row.index:
                    if col in df_result.columns:
                        if pd.notna(new_row[col]) and str(new_row[col]).strip() != '':
                            old_value = df_result.loc[existing_index, col]
                            df_result.loc[existing_index, col] = new_row[col]
                            print(f"   {col}: '{old_value}' -> '{new_row[col]}'")
            else:
                # Ajouter nouvelle ligne
                print(f"\n➕ Ajout d'une nouvelle ligne pour {key_column}: {key_value}")
                df_result = pd.concat([df_result, new_row.to_frame().T], ignore_index=True)
        
        print("\n📊 Résultat final:")
        print(df_result)
        print("✅ Test de consolidation réussi")
        
    except Exception as e:
        print(f"❌ Erreur dans la logique de consolidation: {e}")
        import traceback
        traceback.print_exc()

def test_file_operations():
    """Test des opérations sur fichiers"""
    print("\n💾 Test des opérations sur fichiers...")
    
    # Test de création de sauvegarde
    global_file = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    if os.path.exists(global_file):
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = os.path.dirname(global_file)
            original_name = os.path.splitext(os.path.basename(global_file))[0]
            backup_name = f"{original_name}_backup_{timestamp}.xlsx"
            backup_path = os.path.join(backup_dir, backup_name)
            
            import shutil
            shutil.copy2(global_file, backup_path)
            print(f"✅ Sauvegarde créée: {backup_path}")
            
            # Nettoyer
            os.remove(backup_path)
            print("✅ Sauvegarde test supprimée")
            
        except Exception as e:
            print(f"❌ Erreur création sauvegarde: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST DE DÉBOGAGE - CONSOLIDATION")
    print("=" * 60)
    
    test_read_excel_files()
    test_consolidation_logic()
    test_file_operations()
    
    print("\n" + "=" * 60)
    print("🏁 TESTS TERMINÉS")
    print("=" * 60)
