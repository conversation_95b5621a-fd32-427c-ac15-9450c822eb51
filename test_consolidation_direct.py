"""
Test direct de la fonctionnalité de consolidation
"""

import pandas as pd
import os
import sys
from datetime import datetime
import shutil

def test_direct_consolidation():
    """Test direct de la consolidation sans GUI"""
    print("🧪 Test direct de la consolidation...")
    
    # Fichiers
    global_file = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    test_file = "Test_Suivi_Commune_PA-TEST-001_99999.xlsx"
    
    if not os.path.exists(global_file):
        print(f"❌ Fichier Suivi Global non trouvé: {global_file}")
        return False
    
    if not os.path.exists(test_file):
        print(f"❌ Fichier de test non trouvé: {test_file}")
        return False
    
    try:
        # 1. Créer une sauvegarde
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"Suivi_Global_Plan_Adressage_20250603_1305_backup_{timestamp}.xlsx"
        shutil.copy2(global_file, backup_name)
        print(f"✅ Sauvegarde créée: {backup_name}")
        
        # 2. Charger le fichier Suivi Global
        df_global = pd.read_excel(global_file)
        print(f"✅ Suivi Global chargé: {len(df_global)} lignes")
        
        # 3. Extraire les données du fichier de test
        excel_file = pd.ExcelFile(test_file)
        target_sheet = None
        for sheet_name in excel_file.sheet_names:
            if "informations commune" in sheet_name.lower():
                target_sheet = sheet_name
                break
        
        if not target_sheet:
            print(f"❌ Feuille 'Informations Commune' non trouvée dans {test_file}")
            return False
        
        df_new = pd.read_excel(test_file, sheet_name=target_sheet)
        print(f"✅ Données extraites: {len(df_new)} lignes de {target_sheet}")
        
        # 4. Ajouter les colonnes de traçabilité
        df_new['Source_Fichier'] = os.path.basename(test_file)
        df_new['Date_Consolidation'] = datetime.now().strftime("%d/%m/%Y %H:%M")
        
        # 5. Fusionner les données
        print("🔄 Fusion des données...")
        
        # Identifier la colonne clé
        key_columns = ['Code INSEE', 'Nom de commune', 'ID tâche Plan Adressage']
        key_column = None
        
        for col in key_columns:
            if col in df_global.columns and col in df_new.columns:
                key_column = col
                break
        
        if not key_column:
            print("❌ Aucune colonne clé trouvée pour la fusion")
            return False
        
        print(f"✅ Utilisation de la colonne clé: {key_column}")
        
        # Créer le résultat
        df_result = df_global.copy()
        
        for _, new_row in df_new.iterrows():
            key_value = new_row[key_column]
            existing_mask = df_result[key_column] == key_value
            
            if existing_mask.any():
                # Mettre à jour la ligne existante
                existing_index = df_result[existing_mask].index[0]
                print(f"🔄 Mise à jour pour {key_column}: {key_value}")
                
                for col in new_row.index:
                    if col in df_result.columns:
                        if pd.notna(new_row[col]) and str(new_row[col]).strip() != '':
                            df_result.loc[existing_index, col] = new_row[col]
            else:
                # Ajouter nouvelle ligne
                print(f"➕ Ajout nouveau pour {key_column}: {key_value}")
                df_result = pd.concat([df_result, new_row.to_frame().T], ignore_index=True)
        
        # 6. Sauvegarder le résultat
        df_result.to_excel(global_file, index=False)
        print(f"✅ Fichier Suivi Global mis à jour: {len(df_result)} lignes")
        
        # 7. Afficher le résultat
        print("\n📊 Résultat final:")
        print(df_result[['Nom de commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Etat Ticket PA', 'Collaborateur']].to_string())
        
        print(f"\n✅ Consolidation réussie!")
        print(f"   Sauvegarde: {backup_name}")
        print(f"   Fichier mis à jour: {global_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la consolidation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST DIRECT DE CONSOLIDATION")
    print("=" * 60)
    
    success = test_direct_consolidation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TEST RÉUSSI - La consolidation fonctionne!")
    else:
        print("❌ TEST ÉCHOUÉ - Problème dans la consolidation")
    print("=" * 60)
