# 🔄 Consolidateur Suivi Global

Application standalone pour consolider les fichiers Excel individuels vers le fichier Suivi Global dans Microsoft Teams.

## 📋 Description

Cette application remplace la macro Excel partagée dans le canal Teams en fournissant une solution Python robuste pour:

- **Extraire** les données de la feuille "Informations Commune" des fichiers individuels
- **Consolider** vers le fichier Suivi Global existant dans Teams
- **Gérer les doublons** en mettant à jour les communes existantes
- **Créer des sauvegardes** automatiques avant modification
- **Tracer les modifications** avec horodatage et source

## 🚀 Utilisation

### **Prérequis**
- Windows avec Microsoft Teams installé et synchronisé
- Accès au canal Teams contenant le fichier Suivi Global
- Fichiers Excel générés par l'application Suivi_Generator

### **Étapes d'utilisation**

1. **Lancer l'application**
   - Double-cliquez sur `Suivi_Global_Consolidator.exe`

2. **Sélectionner le fichier Suivi Global**
   - Cliquez sur "📂 Sélectionner Suivi Global"
   - Naviguez vers le dossier Teams synchronisé
   - Sélectionnez le fichier Suivi Global existant

3. **Ajouter les fichiers à consolider**
   - Cliquez sur "➕ Ajouter Fichiers"
   - Sélectionnez un ou plusieurs fichiers Excel individuels
   - Les fichiers doivent contenir une feuille "Informations Commune"

4. **Lancer la consolidation**
   - Cliquez sur "🔄 Consolider vers Suivi Global"
   - Confirmez l'opération
   - Attendez la fin du traitement

## 🔧 Fonctionnalités

### **Gestion des doublons**
- **Détection automatique** basée sur le Code INSEE ou Nom de commune
- **Mise à jour** des lignes existantes avec les nouvelles données
- **Ajout** de nouvelles lignes pour les communes non existantes

### **Sécurité**
- **Sauvegarde automatique** avant toute modification
- **Validation** des fichiers avant traitement
- **Gestion d'erreurs** robuste avec messages informatifs

### **Traçabilité**
- **Source_Fichier**: Nom du fichier d'origine
- **Date_Consolidation**: Horodatage de la consolidation
- **Logs détaillés** dans `consolidator.log`

## 📁 Structure des données

### **Colonnes consolidées** (feuille "Informations Commune"):
- Nom de commune
- ID tâche Plan Adressage
- Code INSEE
- Nbr des voies CM
- Nbr des IMB PA
- Date d'affectation
- Date Livraison
- Etat Ticket PA
- Durée Totale CM
- Duréé Totale PA
- Durée Finale
- ID Tache 501/511
- Date Dépose Ticket 501/511
- Dépose Ticket UPR
- ID tâche UPR
- Collaborateur

### **Colonnes ajoutées automatiquement**:
- **Source_Fichier**: Traçabilité du fichier source
- **Date_Consolidation**: Horodatage de la consolidation

## 🛠️ Compilation

### **Pour développeurs**

```bash
# Installer les dépendances
pip install -r consolidator_requirements.txt

# Compiler l'exécutable
python build_consolidator.py
```

### **Dépendances**
- `pandas>=1.5.0` - Traitement des données Excel
- `openpyxl>=3.0.0` - Lecture/écriture Excel (.xlsx)
- `xlrd>=2.0.0` - Support Excel legacy (.xls)
- `pyinstaller>=5.0.0` - Création d'exécutables

## 📊 Interface utilisateur

### **Sections principales**:

1. **📁 Fichier Suivi Global (Teams)**
   - Sélection du fichier maître à mettre à jour

2. **📄 Fichiers à Consolider**
   - Liste des fichiers individuels à traiter
   - Ajout/suppression de fichiers

3. **⚡ Consolidation**
   - Bouton de lancement du processus

4. **📊 Statut**
   - Progression en temps réel
   - Messages d'état et d'erreur

## 🔍 Détection automatique Teams

L'application détecte automatiquement les dossiers Teams synchronisés dans:
- `%USERPROFILE%\OneDrive - orange.com`
- `%USERPROFILE%\OneDrive`
- `%USERPROFILE%\Documents`
- `%USERPROFILE%\Desktop`

## 📝 Logs et débogage

- **Fichier de log**: `consolidator.log`
- **Niveau de détail**: INFO (opérations) + ERROR (erreurs)
- **Rotation**: Nouveau log à chaque démarrage

## ⚠️ Notes importantes

- **Sauvegarde**: Une sauvegarde est créée automatiquement avant chaque consolidation
- **Permissions**: L'utilisateur doit avoir accès en écriture au fichier Suivi Global
- **Synchronisation**: Assurez-vous que Teams est synchronisé avant utilisation
- **Format**: Seuls les fichiers Excel (.xlsx, .xls) sont supportés

## 🆘 Support

En cas de problème:
1. Vérifiez le fichier `consolidator.log`
2. Assurez-vous que Teams est synchronisé
3. Vérifiez les permissions d'accès aux fichiers
4. Contactez l'équipe de développement avec les logs d'erreur
