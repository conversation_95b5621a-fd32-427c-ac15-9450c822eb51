"""
Consolidateur Suivi Global - Application de consolidation des fichiers Excel
Consolide les données de la feuille "Informations Commune" vers le fichier Suivi Global dans Teams
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
import sys
import logging
from datetime import datetime
from pathlib import Path
import shutil

# Configuration des couleurs (cohérent avec l'app principale)
COLORS = {
    'PRIMARY': "#FF6600",      # Orange principal
    'SECONDARY': "#2E86AB",    # Bleu secondaire
    'SUCCESS': "#28A745",      # Vert succès
    'WARNING': "#FFC107",      # Jaune avertissement
    'DANGER': "#DC3545",       # Rouge erreur
    'BG': "#F8F9FA",          # Arrière-plan clair
    'CARD': "#FFFFFF",        # Cartes blanches
    'BORDER': "#DEE2E6",      # Bordures grises
    'TEXT': "#495057",        # Texte principal
    'TEXT_LIGHT': "#6C757D"   # Texte secondaire
}

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('consolidator.log'),
        logging.StreamHandler()
    ]
)

class SuiviGlobalConsolidator:
    """
    Application de consolidation des fichiers Excel vers Suivi Global.
    
    Cette application permet de:
    - Sélectionner des fichiers Excel individuels
    - Extraire les données de la feuille "Informations Commune"
    - Consolider vers un fichier Suivi Global existant
    - Gérer les doublons en mettant à jour les communes existantes
    """
    
    # Configuration de l'interface
    WINDOW_TITLE = "Consolidateur Suivi Global - Plan Adressage"
    WINDOW_SIZE = "800x600"
    WINDOW_MIN_SIZE = "700x500"
    
    # Typographie
    FONT_TITLE = ("Segoe UI", 12, "bold")
    FONT_SUBTITLE = ("Segoe UI", 10)
    FONT_BUTTON = ("Segoe UI", 9, "bold")
    FONT_SMALL = ("Segoe UI", 8)
    
    def __init__(self, root):
        """
        Initialise l'interface de consolidation.
        
        Args:
            root: Fenêtre principale Tkinter
        """
        self.root = root
        self.root.title(self.WINDOW_TITLE)
        self.root.geometry(self.WINDOW_SIZE)
        self.root.minsize(*map(int, self.WINDOW_MIN_SIZE.split('x')))
        self.root.configure(bg=COLORS['BG'])
        
        # Variables
        self.selected_files = []
        self.suivi_global_path = None
        
        # Configuration du style
        self._setup_styles()
        
        # Interface utilisateur
        self._setup_ui()
        
        # Centrer la fenêtre
        self._center_window()
        
    def _setup_styles(self):
        """Configure les styles ttk pour l'interface."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Style pour les boutons principaux
        style.configure('Primary.TButton',
                       background=COLORS['PRIMARY'],
                       foreground='white',
                       font=self.FONT_BUTTON,
                       padding=(10, 5))
        
        # Style pour les boutons de succès
        style.configure('Success.TButton',
                       background=COLORS['SUCCESS'],
                       foreground='white',
                       font=self.FONT_BUTTON,
                       padding=(10, 5))
        
        # Style pour les boutons d'avertissement
        style.configure('Warning.TButton',
                       background=COLORS['WARNING'],
                       foreground='black',
                       font=self.FONT_BUTTON,
                       padding=(10, 5))
    
    def _center_window(self):
        """Centre la fenêtre sur l'écran."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _setup_ui(self):
        """Configure l'interface utilisateur."""
        # Frame principal
        main_frame = tk.Frame(self.root, bg=COLORS['BG'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # En-tête
        self._setup_header(main_frame)
        
        # Section de sélection du fichier Suivi Global
        self._setup_global_file_section(main_frame)
        
        # Section de sélection des fichiers individuels
        self._setup_individual_files_section(main_frame)
        
        # Section de consolidation
        self._setup_consolidation_section(main_frame)
        
        # Section de statut
        self._setup_status_section(main_frame)
    
    def _setup_header(self, parent):
        """Configure l'en-tête de l'application."""
        header_frame = tk.Frame(parent, bg=COLORS['BG'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Titre principal
        title_label = tk.Label(
            header_frame,
            text="🔄 Consolidateur Suivi Global",
            font=self.FONT_TITLE,
            fg=COLORS['PRIMARY'],
            bg=COLORS['BG']
        )
        title_label.pack()
        
        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Consolidation des données vers le fichier Suivi Global Teams",
            font=self.FONT_SUBTITLE,
            fg=COLORS['TEXT'],
            bg=COLORS['BG']
        )
        subtitle_label.pack(pady=(5, 0))
    
    def _setup_global_file_section(self, parent):
        """Configure la section de sélection du fichier Suivi Global."""
        # Carte pour le fichier Suivi Global
        global_card = tk.Frame(parent, bg=COLORS['CARD'], relief=tk.RAISED, bd=1)
        global_card.pack(fill=tk.X, pady=(0, 15))
        
        # En-tête de la carte
        header_frame = tk.Frame(global_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        tk.Label(
            header_frame,
            text="📁 Fichier Suivi Global (Teams)",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)
        
        # Contenu de la carte
        content_frame = tk.Frame(global_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # Boutons de sélection
        buttons_frame = tk.Frame(content_frame, bg=COLORS['CARD'])
        buttons_frame.pack(side=tk.LEFT, padx=(0, 10))

        self.btn_select_global = ttk.Button(
            buttons_frame,
            text="📂 Sélectionner Suivi Global",
            style='Primary.TButton',
            command=self._select_global_file
        )
        self.btn_select_global.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_auto_find = ttk.Button(
            buttons_frame,
            text="🔍 Auto-Trouver",
            style='Success.TButton',
            command=self._auto_find_and_select
        )
        self.btn_auto_find.pack(side=tk.LEFT)

        # Label d'état
        self.label_global_file = tk.Label(
            content_frame,
            text="Aucun fichier sélectionné - Cliquez 'Auto-Trouver' ou 'Sélectionner'",
            font=self.FONT_SMALL,
            fg=COLORS['TEXT_LIGHT'],
            bg=COLORS['CARD']
        )
        self.label_global_file.pack(side=tk.LEFT, padx=(10, 0))
    
    def _setup_individual_files_section(self, parent):
        """Configure la section de sélection des fichiers individuels."""
        # Carte pour les fichiers individuels
        files_card = tk.Frame(parent, bg=COLORS['CARD'], relief=tk.RAISED, bd=1)
        files_card.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # En-tête de la carte
        header_frame = tk.Frame(files_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        tk.Label(
            header_frame,
            text="📄 Fichiers à Consolider",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = tk.Frame(files_card, bg=COLORS['CARD'])
        buttons_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        self.btn_add_files = ttk.Button(
            buttons_frame,
            text="➕ Ajouter Fichiers",
            style='Primary.TButton',
            command=self._add_files
        )
        self.btn_add_files.pack(side=tk.LEFT, padx=(0, 10))
        
        self.btn_clear_files = ttk.Button(
            buttons_frame,
            text="🗑️ Vider Liste",
            style='Warning.TButton',
            command=self._clear_files
        )
        self.btn_clear_files.pack(side=tk.LEFT)
        
        # Liste des fichiers avec scrollbar
        list_frame = tk.Frame(files_card, bg=COLORS['CARD'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # Scrollbar
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Listbox
        self.files_listbox = tk.Listbox(
            list_frame,
            yscrollcommand=scrollbar.set,
            font=self.FONT_SMALL,
            bg='white',
            selectmode=tk.EXTENDED
        )
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.files_listbox.yview)
        
        # Bouton de suppression des fichiers sélectionnés
        self.btn_remove_selected = ttk.Button(
            files_card,
            text="➖ Supprimer Sélectionnés",
            style='Warning.TButton',
            command=self._remove_selected_files
        )
        self.btn_remove_selected.pack(padx=15, pady=(0, 15))

    def _setup_consolidation_section(self, parent):
        """Configure la section de consolidation."""
        # Carte de consolidation
        consolidation_card = tk.Frame(parent, bg=COLORS['CARD'], relief=tk.RAISED, bd=1)
        consolidation_card.pack(fill=tk.X, pady=(0, 15))

        # En-tête de la carte
        header_frame = tk.Frame(consolidation_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        tk.Label(
            header_frame,
            text="⚡ Consolidation",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        # Bouton de consolidation
        self.btn_consolidate = ttk.Button(
            consolidation_card,
            text="🔄 Consolider vers Suivi Global",
            style='Success.TButton',
            command=self._consolidate_files,
            state=tk.DISABLED
        )
        self.btn_consolidate.pack(padx=15, pady=(0, 15))

    def _setup_status_section(self, parent):
        """Configure la section de statut."""
        # Carte de statut
        status_card = tk.Frame(parent, bg=COLORS['CARD'], relief=tk.RAISED, bd=1)
        status_card.pack(fill=tk.X)

        # En-tête de la carte
        header_frame = tk.Frame(status_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        tk.Label(
            header_frame,
            text="📊 Statut",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        # Contenu du statut
        status_content = tk.Frame(status_card, bg=COLORS['CARD'])
        status_content.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Icône et texte de statut
        self.status_icon = tk.Label(
            status_content,
            text="⏳",
            font=("Segoe UI", 14),
            bg=COLORS['CARD']
        )
        self.status_icon.pack(side=tk.LEFT, padx=(0, 10))

        self.status_label = tk.Label(
            status_content,
            text="Prêt - Sélectionnez les fichiers pour commencer",
            font=self.FONT_SMALL,
            fg=COLORS['TEXT'],
            bg=COLORS['CARD']
        )
        self.status_label.pack(side=tk.LEFT)

        # Barre de progression
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_card,
            variable=self.progress_var,
            maximum=100,
            length=300
        )
        self.progress_bar.pack(padx=15, pady=(0, 15))

    def _select_global_file(self):
        """Sélectionne le fichier Suivi Global."""
        try:
            # Détecter les dossiers Teams communs
            teams_paths = self._detect_teams_folders()
            initial_dir = teams_paths[0] if teams_paths else os.path.expanduser("~")

            file_path = filedialog.askopenfilename(
                title="Sélectionner le fichier Suivi Global",
                filetypes=[("Fichiers Excel", "*.xlsx *.xls")],
                initialdir=initial_dir
            )

            if file_path:
                self.suivi_global_path = file_path
                filename = os.path.basename(file_path)
                self.label_global_file.config(
                    text=f"✅ {filename[:60]}{'...' if len(filename) > 60 else ''}",
                    fg=COLORS['SUCCESS']
                )
                self.btn_select_global.config(text="✅ Suivi Global OK")
                self._update_consolidation_status()
                logging.info(f"Fichier Suivi Global sélectionné: {filename}")

        except Exception as e:
            logging.error(f"Erreur lors de la sélection du fichier Suivi Global: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sélection du fichier:\n{e}")

    def _detect_teams_folders(self):
        """Détecte les dossiers Teams synchronisés localement."""
        teams_paths = []
        user_profile = os.path.expanduser("~")

        # Chemins spécifiques pour votre organisation Orange
        orange_paths = [
            # OneDrive Orange principal
            os.path.join(user_profile, "OneDrive - orange.com"),

            # Dossier Teams spécifique pour votre canal
            os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés"),
            os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage"),
            os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),

            # Autres chemins Teams possibles
            os.path.join(user_profile, "OneDrive - orange.com", "Documents partagés"),
            os.path.join(user_profile, "OneDrive - orange.com", "CM Adresses et Plan Adressage"),
        ]

        # Chemins génériques de fallback
        fallback_paths = [
            os.path.join(user_profile, "OneDrive"),
            os.path.join(user_profile, "Documents"),
            os.path.join(user_profile, "Desktop")
        ]

        # Vérifier d'abord les chemins Orange spécifiques
        for path in orange_paths:
            if os.path.exists(path):
                teams_paths.append(path)
                logging.info(f"Dossier Teams Orange détecté: {path}")

        # Ajouter les chemins de fallback s'ils existent
        for path in fallback_paths:
            if os.path.exists(path) and path not in teams_paths:
                teams_paths.append(path)

        return teams_paths

    def _auto_find_suivi_global(self):
        """Tente de trouver automatiquement le fichier Suivi Global."""
        try:
            teams_paths = self._detect_teams_folders()

            # Patterns de noms de fichiers à rechercher
            file_patterns = [
                "Suivi_Global_Plan_Adressage*.xlsx",
                "Suivi_Global*.xlsx",
                "suivi_global*.xlsx",
                "SuiviGlobal*.xlsx"
            ]

            for base_path in teams_paths:
                for pattern in file_patterns:
                    # Rechercher dans le dossier et ses sous-dossiers
                    for root, dirs, files in os.walk(base_path):
                        for file in files:
                            if any(file.lower().startswith(p.lower().replace("*", "").replace(".xlsx", ""))
                                  and file.lower().endswith(".xlsx") for p in file_patterns):
                                full_path = os.path.join(root, file)
                                logging.info(f"Fichier Suivi Global trouvé automatiquement: {full_path}")
                                return full_path

            return None

        except Exception as e:
            logging.error(f"Erreur lors de la recherche automatique: {e}")
            return None

    def _auto_find_and_select(self):
        """Auto-trouve et sélectionne le fichier Suivi Global."""
        try:
            self.status_label.config(text="Recherche automatique du fichier Suivi Global...", fg=COLORS['PRIMARY'])
            self.root.update()

            found_path = self._auto_find_suivi_global()

            if found_path:
                self.suivi_global_path = found_path
                filename = os.path.basename(found_path)
                self.label_global_file.config(
                    text=f"🔍 Auto-trouvé: {filename[:50]}{'...' if len(filename) > 50 else ''}",
                    fg=COLORS['SUCCESS']
                )
                self.btn_select_global.config(text="✅ Suivi Global OK")
                self.btn_auto_find.config(text="✅ Trouvé")
                self._update_consolidation_status()

                messagebox.showinfo(
                    "Fichier trouvé automatiquement",
                    f"Le fichier Suivi Global a été trouvé automatiquement:\n\n{found_path}\n\nVous pouvez maintenant ajouter des fichiers à consolider."
                )
                logging.info(f"Fichier Suivi Global trouvé automatiquement: {filename}")
            else:
                self.status_label.config(text="Aucun fichier Suivi Global trouvé automatiquement", fg=COLORS['WARNING'])
                messagebox.showwarning(
                    "Fichier non trouvé",
                    "Aucun fichier Suivi Global n'a été trouvé automatiquement.\n\n"
                    "Vérifiez que:\n"
                    "• Teams est synchronisé sur votre PC\n"
                    "• Le fichier existe dans le canal Teams\n"
                    "• Le nom du fichier contient 'Suivi_Global'\n\n"
                    "Utilisez 'Sélectionner Suivi Global' pour choisir manuellement."
                )

        except Exception as e:
            logging.error(f"Erreur lors de la recherche automatique: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la recherche automatique:\n{e}")

    def _add_files(self):
        """Ajoute des fichiers à la liste de consolidation."""
        try:
            file_paths = filedialog.askopenfilenames(
                title="Sélectionner les fichiers à consolider",
                filetypes=[("Fichiers Excel", "*.xlsx *.xls")]
            )

            for file_path in file_paths:
                if file_path not in self.selected_files:
                    # Vérifier que le fichier contient une feuille "Informations Commune"
                    if self._validate_excel_file(file_path):
                        self.selected_files.append(file_path)
                        filename = os.path.basename(file_path)
                        self.files_listbox.insert(tk.END, f"{filename} - {file_path}")
                        logging.info(f"Fichier ajouté: {filename}")
                    else:
                        messagebox.showwarning(
                            "Fichier invalide",
                            f"Le fichier '{os.path.basename(file_path)}' ne contient pas de feuille 'Informations Commune'."
                        )

            self._update_consolidation_status()

        except Exception as e:
            logging.error(f"Erreur lors de l'ajout de fichiers: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout de fichiers:\n{e}")

    def _validate_excel_file(self, file_path):
        """Valide qu'un fichier Excel contient la feuille requise."""
        try:
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names

            # Chercher une feuille contenant "Informations Commune"
            for sheet_name in sheet_names:
                if "informations commune" in sheet_name.lower():
                    return True

            return False

        except Exception as e:
            logging.error(f"Erreur lors de la validation du fichier {file_path}: {e}")
            return False

    def _clear_files(self):
        """Vide la liste des fichiers sélectionnés."""
        self.selected_files.clear()
        self.files_listbox.delete(0, tk.END)
        self._update_consolidation_status()
        logging.info("Liste des fichiers vidée")

    def _remove_selected_files(self):
        """Supprime les fichiers sélectionnés de la liste."""
        try:
            selected_indices = self.files_listbox.curselection()

            # Supprimer en ordre inverse pour éviter les problèmes d'index
            for index in reversed(selected_indices):
                file_path = self.selected_files[index]
                del self.selected_files[index]
                self.files_listbox.delete(index)
                logging.info(f"Fichier supprimé: {os.path.basename(file_path)}")

            self._update_consolidation_status()

        except Exception as e:
            logging.error(f"Erreur lors de la suppression de fichiers: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la suppression:\n{e}")

    def _update_consolidation_status(self):
        """Met à jour le statut de consolidation."""
        if self.suivi_global_path and self.selected_files:
            self.btn_consolidate.config(state=tk.NORMAL)
            self.status_icon.config(text="✅", fg=COLORS['SUCCESS'])
            self.status_label.config(
                text=f"Prêt à consolider {len(self.selected_files)} fichier(s)",
                fg=COLORS['SUCCESS']
            )
        elif self.suivi_global_path and not self.selected_files:
            self.btn_consolidate.config(state=tk.DISABLED)
            self.status_icon.config(text="⚠️", fg=COLORS['WARNING'])
            self.status_label.config(
                text="Suivi Global sélectionné - Ajoutez des fichiers à consolider",
                fg=COLORS['WARNING']
            )
        elif not self.suivi_global_path and self.selected_files:
            self.btn_consolidate.config(state=tk.DISABLED)
            self.status_icon.config(text="⚠️", fg=COLORS['WARNING'])
            self.status_label.config(
                text="Fichiers sélectionnés - Sélectionnez le Suivi Global",
                fg=COLORS['WARNING']
            )
        else:
            self.btn_consolidate.config(state=tk.DISABLED)
            self.status_icon.config(text="⏳", fg=COLORS['TEXT'])
            self.status_label.config(
                text="Prêt - Sélectionnez les fichiers pour commencer",
                fg=COLORS['TEXT']
            )

    def _consolidate_files(self):
        """Lance le processus de consolidation."""
        try:
            if not self.suivi_global_path or not self.selected_files:
                messagebox.showwarning("Consolidation impossible",
                                     "Veuillez sélectionner le fichier Suivi Global et au moins un fichier à consolider.")
                return

            # Confirmation avant consolidation
            result = messagebox.askyesno(
                "Confirmer la consolidation",
                f"Êtes-vous sûr de vouloir consolider {len(self.selected_files)} fichier(s) vers le Suivi Global?\n\n"
                f"Cette opération va modifier le fichier:\n{os.path.basename(self.suivi_global_path)}"
            )

            if not result:
                return

            # Démarrer la consolidation
            self._perform_consolidation()

        except Exception as e:
            logging.error(f"Erreur lors de la consolidation: {e}")
            messagebox.showerror("Erreur de consolidation", f"Une erreur est survenue:\n{e}")

    def _perform_consolidation(self):
        """Effectue la consolidation des données."""
        try:
            # Initialiser la barre de progression
            self.progress_var.set(0)
            self.status_icon.config(text="🔄", fg=COLORS['PRIMARY'])
            self.status_label.config(text="Consolidation en cours...", fg=COLORS['PRIMARY'])
            self.root.update()

            # Créer une sauvegarde du fichier Suivi Global
            backup_path = self._create_backup()
            logging.info(f"Sauvegarde créée: {backup_path}")

            # Charger le fichier Suivi Global existant
            self.progress_var.set(10)
            self.root.update()

            try:
                df_global = pd.read_excel(self.suivi_global_path)
                logging.info(f"Fichier Suivi Global chargé: {len(df_global)} lignes existantes")
            except Exception as e:
                # Si le fichier n'existe pas ou est vide, créer un DataFrame vide
                df_global = pd.DataFrame()
                logging.info("Création d'un nouveau fichier Suivi Global")

            # Traiter chaque fichier individuel
            total_files = len(self.selected_files)
            consolidated_data = []

            for i, file_path in enumerate(self.selected_files):
                try:
                    # Mettre à jour la progression
                    progress = 10 + (i / total_files) * 70
                    self.progress_var.set(progress)
                    self.status_label.config(text=f"Traitement: {os.path.basename(file_path)}")
                    self.root.update()

                    # Extraire les données de la feuille "Informations Commune"
                    data = self._extract_commune_data(file_path)
                    if data is not None:
                        consolidated_data.append(data)
                        logging.info(f"Données extraites de: {os.path.basename(file_path)}")

                except Exception as e:
                    logging.error(f"Erreur lors du traitement de {file_path}: {e}")
                    messagebox.showwarning(
                        "Erreur de traitement",
                        f"Impossible de traiter le fichier:\n{os.path.basename(file_path)}\n\nErreur: {e}"
                    )

            # Consolider les données
            self.progress_var.set(80)
            self.status_label.config(text="Consolidation des données...")
            self.root.update()

            if consolidated_data:
                df_new = pd.concat(consolidated_data, ignore_index=True)
                df_final = self._merge_data(df_global, df_new)

                # Sauvegarder le fichier consolidé
                self.progress_var.set(90)
                self.status_label.config(text="Sauvegarde du fichier consolidé...")
                self.root.update()

                df_final.to_excel(self.suivi_global_path, index=False)

                # Finaliser
                self.progress_var.set(100)
                self.status_icon.config(text="✅", fg=COLORS['SUCCESS'])
                self.status_label.config(
                    text=f"Consolidation terminée - {len(df_new)} ligne(s) ajoutée(s)/mise(s) à jour",
                    fg=COLORS['SUCCESS']
                )

                messagebox.showinfo(
                    "Consolidation réussie",
                    f"La consolidation a été effectuée avec succès!\n\n"
                    f"• {len(df_new)} ligne(s) traitée(s)\n"
                    f"• Fichier mis à jour: {os.path.basename(self.suivi_global_path)}\n"
                    f"• Sauvegarde disponible: {os.path.basename(backup_path)}"
                )

                logging.info(f"Consolidation terminée avec succès: {len(df_new)} lignes traitées")

            else:
                self.status_icon.config(text="⚠️", fg=COLORS['WARNING'])
                self.status_label.config(text="Aucune donnée à consolider", fg=COLORS['WARNING'])
                messagebox.showwarning("Aucune donnée", "Aucune donnée valide n'a pu être extraite des fichiers sélectionnés.")

        except Exception as e:
            self.progress_var.set(0)
            self.status_icon.config(text="❌", fg=COLORS['DANGER'])
            self.status_label.config(text="Erreur lors de la consolidation", fg=COLORS['DANGER'])
            logging.error(f"Erreur lors de la consolidation: {e}")
            raise

    def _create_backup(self):
        """Crée une sauvegarde du fichier Suivi Global."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = os.path.dirname(self.suivi_global_path)
        original_name = os.path.splitext(os.path.basename(self.suivi_global_path))[0]
        backup_name = f"{original_name}_backup_{timestamp}.xlsx"
        backup_path = os.path.join(backup_dir, backup_name)

        shutil.copy2(self.suivi_global_path, backup_path)
        return backup_path

    def _extract_commune_data(self, file_path):
        """Extrait les données de la feuille 'Informations Commune' d'un fichier."""
        try:
            excel_file = pd.ExcelFile(file_path)

            # Trouver la feuille "Informations Commune"
            target_sheet = None
            for sheet_name in excel_file.sheet_names:
                if "informations commune" in sheet_name.lower():
                    target_sheet = sheet_name
                    break

            if not target_sheet:
                logging.warning(f"Feuille 'Informations Commune' non trouvée dans {file_path}")
                return None

            # Lire les données
            df = pd.read_excel(file_path, sheet_name=target_sheet)

            # Vérifier que les données ne sont pas vides
            if df.empty:
                logging.warning(f"Feuille 'Informations Commune' vide dans {file_path}")
                return None

            # Ajouter une colonne pour tracer la source du fichier
            df['Source_Fichier'] = os.path.basename(file_path)
            df['Date_Consolidation'] = datetime.now().strftime("%d/%m/%Y %H:%M")

            return df

        except Exception as e:
            logging.error(f"Erreur lors de l'extraction des données de {file_path}: {e}")
            return None

    def _merge_data(self, df_global, df_new):
        """Fusionne les nouvelles données avec les données existantes."""
        try:
            if df_global.empty:
                # Si le fichier global est vide, retourner directement les nouvelles données
                return df_new

            # Identifier la colonne clé pour les doublons (Code INSEE ou Nom de commune)
            key_columns = []

            # Chercher les colonnes communes pour identifier les doublons
            for col in ['Code INSEE', 'Nom de commune', 'ID tâche Plan Adressage']:
                if col in df_global.columns and col in df_new.columns:
                    key_columns.append(col)

            if not key_columns:
                # Si aucune colonne clé trouvée, simplement concaténer
                logging.warning("Aucune colonne clé trouvée pour la détection des doublons")
                return pd.concat([df_global, df_new], ignore_index=True)

            # Utiliser la première colonne clé trouvée (priorité: Code INSEE)
            key_column = key_columns[0]
            logging.info(f"Utilisation de la colonne '{key_column}' pour la détection des doublons")

            # Créer une copie du DataFrame global
            df_result = df_global.copy()

            # Traiter chaque ligne des nouvelles données
            for _, new_row in df_new.iterrows():
                key_value = new_row[key_column]

                # Chercher si cette valeur existe déjà
                existing_mask = df_result[key_column] == key_value

                if existing_mask.any():
                    # Mettre à jour la ligne existante
                    existing_index = df_result[existing_mask].index[0]
                    for col in new_row.index:
                        if col in df_result.columns:
                            # Mettre à jour seulement si la nouvelle valeur n'est pas vide
                            if pd.notna(new_row[col]) and str(new_row[col]).strip() != '':
                                df_result.loc[existing_index, col] = new_row[col]

                    logging.info(f"Mise à jour de la ligne existante pour {key_column}: {key_value}")
                else:
                    # Ajouter une nouvelle ligne
                    df_result = pd.concat([df_result, new_row.to_frame().T], ignore_index=True)
                    logging.info(f"Ajout d'une nouvelle ligne pour {key_column}: {key_value}")

            return df_result

        except Exception as e:
            logging.error(f"Erreur lors de la fusion des données: {e}")
            # En cas d'erreur, retourner une simple concaténation
            return pd.concat([df_global, df_new], ignore_index=True)


# Fonction principale
def main():
    """Point d'entrée principal de l'application de consolidation."""
    try:
        root = tk.Tk()
        app = SuiviGlobalConsolidator(root)
        root.mainloop()
    except Exception as e:
        logging.error(f"Erreur fatale de l'application: {e}")
        messagebox.showerror("Erreur fatale", f"Une erreur fatale est survenue:\n{e}")


if __name__ == "__main__":
    main()
