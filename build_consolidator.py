"""
Script de compilation pour le Consolidateur Suivi Global
Crée un exécutable standalone avec PyInstaller
"""

import subprocess
import sys
import os
from pathlib import Path

# Configuration
APP_NAME = "Suivi_Global_Consolidator"
SCRIPT_PATH = "Suivi_Global_Consolidator.py"
REQUIREMENTS_FILE = "consolidator_requirements.txt"

def print_header():
    """Affiche l'en-tête du script de compilation."""
    print("=" * 60)
    print("🔧 COMPILATION CONSOLIDATEUR SUIVI GLOBAL")
    print("=" * 60)
    print()

def install_dependencies():
    """Installe les dépendances nécessaires."""
    print("📦 Installation des dépendances...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", REQUIREMENTS_FILE],
                      check=True, capture_output=True, text=True)
        print("   ✅ Dépendances installées avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erreur lors de l'installation: {e}")
        print(f"   Détails: {e.stderr if hasattr(e, 'stderr') else 'Aucun détail'}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur inattendue: {e}")
        return False

def build_executable():
    """Compile l'application avec PyInstaller."""
    print("🔨 Compilation avec PyInstaller...")
    
    try:
        # Commande PyInstaller pour le consolidateur
        cmd = [
            "pyinstaller",
            "--onefile",                    # Un seul fichier exécutable
            "--windowed",                   # Pas de console
            "--clean",                      # Nettoyer avant build
            "--noconfirm",                  # Pas de confirmation
            f"--name={APP_NAME}",          # Nom de l'exécutable
            "--hidden-import=pandas",       # Imports cachés
            "--hidden-import=openpyxl",
            "--hidden-import=xlrd",
            "--hidden-import=tkinter",
            "--hidden-import=tkinter.filedialog",
            "--hidden-import=tkinter.messagebox",
            "--hidden-import=tkinter.ttk",
            SCRIPT_PATH
        ]
        
        print(f"   Commande: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("   ✅ Compilation réussie")
        
        # Vérifier que l'exécutable a été créé
        exe_path = Path("dist") / f"{APP_NAME}.exe"
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"   📁 Exécutable créé: {exe_path}")
            print(f"   📏 Taille: {size_mb:.1f} MB")
            return True
        else:
            print("   ❌ Exécutable non trouvé après compilation")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erreur lors de la compilation: {e}")
        if hasattr(e, 'stderr') and e.stderr:
            print(f"   Détails: {e.stderr}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur inattendue: {e}")
        return False

def cleanup():
    """Nettoie les fichiers temporaires."""
    print("🧹 Nettoyage des fichiers temporaires...")
    
    try:
        # Supprimer le dossier build
        import shutil
        if os.path.exists("build"):
            shutil.rmtree("build")
            print("   ✅ Dossier build supprimé")
        
        # Supprimer le fichier .spec
        spec_file = f"{APP_NAME}.spec"
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print("   ✅ Fichier .spec supprimé")
            
    except Exception as e:
        print(f"   ⚠️ Erreur lors du nettoyage: {e}")

def main():
    """Fonction principale de compilation."""
    print_header()
    
    # Vérifier que le script source existe
    if not os.path.exists(SCRIPT_PATH):
        print(f"❌ Fichier source non trouvé: {SCRIPT_PATH}")
        return False
    
    # Vérifier que le fichier requirements existe
    if not os.path.exists(REQUIREMENTS_FILE):
        print(f"❌ Fichier requirements non trouvé: {REQUIREMENTS_FILE}")
        return False
    
    success = True
    
    # Étape 1: Installation des dépendances
    if not install_dependencies():
        success = False
    
    # Étape 2: Compilation
    if success and not build_executable():
        success = False
    
    # Étape 3: Nettoyage
    cleanup()
    
    # Résultat final
    print()
    print("=" * 60)
    if success:
        print("🎉 COMPILATION TERMINÉE AVEC SUCCÈS!")
        print(f"📁 Exécutable disponible dans: dist/{APP_NAME}.exe")
        print()
        print("💡 Instructions:")
        print("   1. Copiez l'exécutable sur les PC des collaborateurs")
        print("   2. Lancez l'application")
        print("   3. Sélectionnez le fichier Suivi Global dans Teams")
        print("   4. Ajoutez les fichiers individuels à consolider")
        print("   5. Cliquez sur 'Consolider vers Suivi Global'")
    else:
        print("❌ ÉCHEC DE LA COMPILATION")
        print("Vérifiez les erreurs ci-dessus et réessayez.")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    main()
