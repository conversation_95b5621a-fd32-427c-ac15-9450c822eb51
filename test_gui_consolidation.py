"""
Test automatisé de la GUI de consolidation
"""

import tkinter as tk
from Suivi_Global_Consolidator import SuiviGlobalConsolidator
import os
import time

def test_gui_consolidation():
    """Test automatisé de la consolidation via GUI"""
    print("🧪 Test automatisé de la GUI de consolidation...")
    
    # Créer l'application
    root = tk.Tk()
    app = SuiviGlobalConsolidator(root)
    
    # Simuler la sélection du fichier Suivi Global
    global_file = "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    test_file = "Test_Suivi_Commune_PA-TEST-001_99999.xlsx"
    
    if not os.path.exists(global_file):
        print(f"❌ Fichier Suivi Global non trouvé: {global_file}")
        root.destroy()
        return False
    
    if not os.path.exists(test_file):
        print(f"❌ Fichier de test non trouvé: {test_file}")
        root.destroy()
        return False
    
    try:
        # Simuler la sélection du fichier global
        app.suivi_global_path = os.path.abspath(global_file)
        app.label_global_file.config(
            text=f"✅ {os.path.basename(global_file)}",
            fg=app.COLORS['SUCCESS'] if hasattr(app, 'COLORS') else 'green'
        )
        app.btn_select_global.config(text="✅ Suivi Global OK")
        
        # Simuler l'ajout du fichier de test
        app.selected_files = [os.path.abspath(test_file)]
        app.files_listbox.insert(tk.END, f"{os.path.basename(test_file)} - {test_file}")
        
        # Mettre à jour le statut
        app._update_consolidation_status()
        
        print(f"✅ Fichiers configurés:")
        print(f"   Global: {app.suivi_global_path}")
        print(f"   Test: {app.selected_files}")
        print(f"   Bouton état: {app.btn_consolidate['state']}")
        
        # Vérifier que le bouton est activé
        if str(app.btn_consolidate['state']) == 'normal':
            print("✅ Bouton consolidation activé")
            
            # Tester la fonction de consolidation directement
            print("🔄 Test de la fonction _consolidate_files...")
            
            # Simuler un clic sur le bouton (sans la boîte de dialogue)
            # On va directement appeler _perform_consolidation pour éviter la confirmation
            try:
                print("🚀 Appel direct de _perform_consolidation...")
                app._perform_consolidation()
                print("✅ _perform_consolidation terminé sans erreur")
                
            except Exception as e:
                print(f"❌ Erreur dans _perform_consolidation: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print(f"❌ Bouton consolidation désactivé: {app.btn_consolidate['state']}")
        
        # Fermer l'application
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans le test GUI: {e}")
        import traceback
        traceback.print_exc()
        root.destroy()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST GUI CONSOLIDATION")
    print("=" * 60)
    
    success = test_gui_consolidation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TEST GUI RÉUSSI")
    else:
        print("❌ TEST GUI ÉCHOUÉ")
    print("=" * 60)
