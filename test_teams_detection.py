"""
Test de détection du fichier Suivi Global dans Teams
Vérifie que l'auto-détection fonctionne avec votre configuration Teams
"""

import os
from pathlib import Path

def test_teams_detection():
    """Test de détection des dossiers Teams et du fichier Suivi Global"""
    print("🔍 Test de détection Teams pour BOTG2R-CMAdressesetPlanAdressage...")
    
    user_profile = os.path.expanduser("~")
    print(f"📁 Profil utilisateur: {user_profile}")
    
    # Chemins spécifiques pour votre organisation Orange
    teams_paths = [
        # OneDrive Orange principal
        os.path.join(user_profile, "OneDrive - orange.com"),
        
        # Dossier Teams spécifique pour votre canal
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage"),
        os.path.join(user_profile, "OneDrive - orange.com", "BOTG2R-CMAdressesetPlanAdressage - Documents partagés", "CM Adresses et Plan Adressage", "Suivie Plan Adressage"),
        
        # Autres chemins Teams possibles
        os.path.join(user_profile, "OneDrive - orange.com", "Documents partagés"),
        os.path.join(user_profile, "OneDrive - orange.com", "CM Adresses et Plan Adressage"),
    ]
    
    print("\n📂 Vérification des chemins Teams:")
    existing_paths = []
    
    for i, path in enumerate(teams_paths, 1):
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"   {i}. {status} {path}")
        if exists:
            existing_paths.append(path)
            
            # Lister le contenu si le dossier existe
            try:
                contents = os.listdir(path)
                if contents:
                    print(f"      📄 Contenu ({len(contents)} éléments): {contents[:5]}{'...' if len(contents) > 5 else ''}")
            except PermissionError:
                print(f"      🔒 Accès refusé")
            except Exception as e:
                print(f"      ⚠️ Erreur: {e}")
    
    print(f"\n📊 Résumé: {len(existing_paths)}/{len(teams_paths)} chemins Teams trouvés")
    
    # Recherche du fichier Suivi Global
    print("\n🔍 Recherche du fichier Suivi Global...")
    
    file_patterns = [
        "Suivi_Global_Plan_Adressage*.xlsx",
        "Suivi_Global*.xlsx", 
        "suivi_global*.xlsx",
        "SuiviGlobal*.xlsx"
    ]
    
    found_files = []
    
    for base_path in existing_paths:
        print(f"\n📁 Recherche dans: {base_path}")
        try:
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if any(file.lower().startswith(p.lower().replace("*", "").replace(".xlsx", "")) 
                          and file.lower().endswith(".xlsx") for p in file_patterns):
                        full_path = os.path.join(root, file)
                        found_files.append(full_path)
                        print(f"   ✅ Trouvé: {file}")
                        print(f"      📍 Chemin: {full_path}")
                        
                        # Vérifier la taille et la date
                        try:
                            stat = os.stat(full_path)
                            size_kb = stat.st_size / 1024
                            from datetime import datetime
                            mod_time = datetime.fromtimestamp(stat.st_mtime)
                            print(f"      📏 Taille: {size_kb:.1f} KB")
                            print(f"      📅 Modifié: {mod_time.strftime('%d/%m/%Y %H:%M')}")
                        except Exception as e:
                            print(f"      ⚠️ Erreur info fichier: {e}")
                            
        except Exception as e:
            print(f"   ❌ Erreur recherche: {e}")
    
    print(f"\n🎯 Résultat final:")
    if found_files:
        print(f"✅ {len(found_files)} fichier(s) Suivi Global trouvé(s)")
        for i, file_path in enumerate(found_files, 1):
            print(f"   {i}. {file_path}")
        
        # Test avec le premier fichier trouvé
        if found_files:
            test_file = found_files[0]
            print(f"\n🧪 Test de lecture du fichier: {os.path.basename(test_file)}")
            try:
                import pandas as pd
                df = pd.read_excel(test_file)
                print(f"   ✅ Lecture réussie: {len(df)} lignes, {len(df.columns)} colonnes")
                print(f"   📊 Colonnes: {list(df.columns)[:5]}{'...' if len(df.columns) > 5 else ''}")
            except Exception as e:
                print(f"   ❌ Erreur lecture: {e}")
    else:
        print("❌ Aucun fichier Suivi Global trouvé")
        print("\n💡 Suggestions:")
        print("   1. Vérifiez que Teams est synchronisé")
        print("   2. Vérifiez que le fichier existe dans le canal")
        print("   3. Attendez la synchronisation complète")
        print("   4. Utilisez la sélection manuelle si nécessaire")
    
    return len(found_files) > 0

if __name__ == "__main__":
    print("=" * 70)
    print("🔍 TEST DE DÉTECTION TEAMS - SUIVI GLOBAL")
    print("=" * 70)
    
    success = test_teams_detection()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 DÉTECTION RÉUSSIE - L'auto-find fonctionnera!")
    else:
        print("⚠️ DÉTECTION ÉCHOUÉE - Utilisez la sélection manuelle")
    print("=" * 70)
