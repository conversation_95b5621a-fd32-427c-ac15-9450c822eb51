"""
Script pour synchroniser le fichier SharePoint vers le dossier Teams local
"""

import os
import webbrowser
from pathlib import Path

def setup_teams_sync_instructions():
    """Fournit les instructions pour synchroniser le fichier SharePoint"""
    
    sharepoint_url = "https://orange0.sharepoint.com/:x:/r/sites/BOTG2R-CMAdressesetPlanAdressage/Documents%20partages/CM%20Adresses%20et%20Plan%20Adressage/Suivie%20Plan%20Adressage/Suivi_Global_Plan_Adressage_20250603_1305.xlsx?d=w86aa865fa57c4e569510134ffdf82101&csf=1&web=1&e=Bc5zbL"
    
    user_profile = os.path.expanduser("~")
    target_folder = os.path.join(
        user_profile, 
        "OneDrive - orange.com", 
        "BOTG2R-CMAdressesetPlanAdressage - Documents partagés",
        "CM Adresses et Plan Adressage",
        "Suivie Plan Adressage"
    )
    
    target_file = os.path.join(target_folder, "Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    
    print("🔄 SYNCHRONISATION SHAREPOINT VERS TEAMS LOCAL")
    print("=" * 60)
    
    print(f"📁 Dossier cible: {target_folder}")
    print(f"📄 Fichier cible: Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    print(f"🔗 URL SharePoint: {sharepoint_url}")
    
    # Vérifier si le dossier Teams existe
    if os.path.exists(target_folder):
        print(f"\n✅ Dossier Teams existe: {target_folder}")
    else:
        print(f"\n📁 Création du dossier Teams...")
        try:
            os.makedirs(target_folder, exist_ok=True)
            print(f"✅ Dossier créé: {target_folder}")
        except Exception as e:
            print(f"❌ Erreur création dossier: {e}")
            return False
    
    # Vérifier si le fichier existe déjà
    if os.path.exists(target_file):
        print(f"\n✅ Le fichier existe déjà localement!")
        
        # Vérifier qu'il est accessible
        try:
            import pandas as pd
            df = pd.read_excel(target_file)
            print(f"📖 Contenu: {len(df)} lignes, {len(df.columns)} colonnes")
            print(f"✅ Le fichier est prêt pour la consolidation!")
            return True
        except Exception as e:
            print(f"❌ Erreur lecture fichier: {e}")
            print(f"Le fichier existe mais n'est pas lisible")
    
    # Instructions pour synchroniser
    print(f"\n📋 INSTRUCTIONS DE SYNCHRONISATION:")
    print(f"")
    print(f"🔄 MÉTHODE 1 - Via Teams (Recommandée):")
    print(f"   1. Ouvrez Microsoft Teams")
    print(f"   2. Allez dans le canal: BOTG2R-CMAdressesetPlanAdressage")
    print(f"   3. Cliquez sur l'onglet 'Fichiers'")
    print(f"   4. Naviguez vers: CM Adresses et Plan Adressage > Suivie Plan Adressage")
    print(f"   5. Trouvez: Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    print(f"   6. Cliquez sur 'Synchroniser' ou 'Toujours conserver sur cet appareil'")
    print(f"   7. Attendez la synchronisation complète")
    print(f"")
    print(f"🌐 MÉTHODE 2 - Via SharePoint:")
    print(f"   1. Le navigateur va s'ouvrir avec le lien SharePoint")
    print(f"   2. Cliquez sur 'Télécharger' dans SharePoint")
    print(f"   3. Sauvegardez le fichier dans: {target_folder}")
    print(f"   4. Renommez-le: Suivi_Global_Plan_Adressage_20250603_1305.xlsx")
    print(f"")
    print(f"⚡ MÉTHODE 3 - Synchronisation automatique OneDrive:")
    print(f"   1. Ouvrez OneDrive dans l'explorateur Windows")
    print(f"   2. Allez dans: OneDrive - orange.com")
    print(f"   3. Cherchez le dossier: BOTG2R-CMAdressesetPlanAdressage - Documents partagés")
    print(f"   4. Si absent, clic droit > 'Toujours conserver sur cet appareil'")
    print(f"   5. Attendez la synchronisation")
    
    # Ouvrir le lien SharePoint
    try:
        print(f"\n🌐 Ouverture du lien SharePoint...")
        webbrowser.open(sharepoint_url)
        print(f"✅ Lien ouvert dans le navigateur")
    except Exception as e:
        print(f"❌ Erreur ouverture navigateur: {e}")
        print(f"Copiez ce lien manuellement: {sharepoint_url}")
    
    return False

def verify_sync_completion():
    """Vérifie si la synchronisation est terminée"""
    
    user_profile = os.path.expanduser("~")
    target_file = os.path.join(
        user_profile, 
        "OneDrive - orange.com", 
        "BOTG2R-CMAdressesetPlanAdressage - Documents partagés",
        "CM Adresses et Plan Adressage",
        "Suivie Plan Adressage",
        "Suivi_Global_Plan_Adressage_20250603_1305.xlsx"
    )
    
    print(f"\n🔍 Vérification de la synchronisation...")
    print(f"📍 Recherche: {target_file}")
    
    if os.path.exists(target_file):
        try:
            import pandas as pd
            df = pd.read_excel(target_file)
            size_kb = os.path.getsize(target_file) / 1024
            
            print(f"✅ SYNCHRONISATION RÉUSSIE!")
            print(f"📊 Fichier: {size_kb:.1f} KB, {len(df)} lignes, {len(df.columns)} colonnes")
            print(f"📍 Emplacement: {target_file}")
            print(f"")
            print(f"🎉 Le consolidateur peut maintenant mettre à jour le fichier SharePoint!")
            print(f"✅ Toutes les modifications seront synchronisées automatiquement")
            
            return True
            
        except Exception as e:
            print(f"❌ Fichier trouvé mais erreur lecture: {e}")
            return False
    else:
        print(f"⏳ Synchronisation en cours ou non démarrée")
        print(f"Suivez les instructions ci-dessus pour synchroniser le fichier")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔄 SYNCHRONISATION FICHIER SHAREPOINT")
    print("=" * 70)
    
    # Étape 1: Instructions de synchronisation
    sync_ready = setup_teams_sync_instructions()
    
    if not sync_ready:
        print(f"\n" + "=" * 70)
        print(f"⏳ EN ATTENTE DE SYNCHRONISATION")
        print(f"Suivez les instructions ci-dessus, puis relancez ce script pour vérifier")
        print(f"=" * 70)
        
        # Demander à l'utilisateur s'il veut vérifier maintenant
        input(f"\nAppuyez sur Entrée après avoir synchronisé le fichier...")
        
        # Vérifier si la synchronisation est terminée
        verify_sync_completion()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 PROCHAINE ÉTAPE: Utilisez le consolidateur avec le bouton 'Auto-Find'")
    print(f"=" * 70)
